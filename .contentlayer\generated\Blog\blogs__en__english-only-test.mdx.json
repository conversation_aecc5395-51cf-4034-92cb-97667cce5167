{"title": "English Only Test Article", "slug": "english-only-test", "description": "This article is only available in English to test the language switching fallback functionality.", "coverImage": "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop", "author": "Test Author", "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": false, "tags": ["test", "english-only", "language-switching"], "body": {"raw": "\n# English Only Test Article\n\nThis article is specifically created to test the language switching functionality when content is not available in all languages.\n\n## Purpose\n\nWhen a user tries to switch to Chinese (中文) while viewing this article, they should be redirected to the Chinese blog list page since this article doesn't have a Chinese version.\n\n## Testing Scenarios\n\n1. **Direct Language Switch**: Use the header language selector\n2. **Language Versions Indicator**: Use the language versions component on this page\n3. **Fallback Behavior**: Verify that users are redirected appropriately\n\n## Expected Behavior\n\n- ✅ English version should be accessible\n- ❌ Chinese version should not exist\n- 🔄 Switching to Chinese should redirect to `/zh/blogs` with a notification\n\nThis helps ensure our intelligent language switching system works correctly in all scenarios.\n", "code": "var Component=(()=>{var je=Object.create;var C=Object.defineProperty;var Oe=Object.getOwnPropertyDescriptor;var Ae=Object.getOwnPropertyNames;var We=Object.getPrototypeOf,Ve=Object.prototype.hasOwnProperty;var $=(a,t)=>()=>(t||a((t={exports:{}}).exports,t),t.exports),Re=(a,t)=>{for(var d in t)C(a,d,{get:t[d],enumerable:!0})},se=(a,t,d,E)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let h of Ae(t))!Ve.call(a,h)&&h!==d&&C(a,h,{get:()=>t[h],enumerable:!(E=Oe(t,h))||E.enumerable});return a};var Pe=(a,t,d)=>(d=a!=null?je(We(a)):{},se(t||!a||!a.__esModule?C(d,\"default\",{value:a,enumerable:!0}):d,a)),De=a=>se(C({},\"__esModule\",{value:!0}),a);var de=$((Xe,ue)=>{ue.exports=React});var fe=$(z=>{\"use strict\";(function(){function a(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===Se?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case O:return\"Fragment\";case xe:return\"Portal\";case K:return\"Profiler\";case H:return\"StrictMode\";case W:return\"Suspense\";case V:return\"SuspenseList\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case F:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case A:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case R:return n=e.displayName||null,n!==null?n:a(e.type)||\"Memo\";case P:n=e._payload,e=e._init;try{return a(e(n))}catch{}}return null}function t(e){return\"\"+e}function d(e){try{t(e);var n=!1}catch{n=!0}if(n){n=console;var r=n.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r.call(n,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),t(e)}}function E(){}function h(){if(v===0){Z=console.log,J=console.info,Q=console.warn,ee=console.error,ne=console.group,re=console.groupCollapsed,te=console.groupEnd;var e={configurable:!0,enumerable:!0,value:E,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}v++}function ge(){if(v--,v===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:_({},e,{value:Z}),info:_({},e,{value:J}),warn:_({},e,{value:Q}),error:_({},e,{value:ee}),group:_({},e,{value:ne}),groupCollapsed:_({},e,{value:re}),groupEnd:_({},e,{value:te})})}0>v&&console.error(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}function w(e){if(Y===void 0)try{throw Error()}catch(r){var n=r.stack.trim().match(/\\n( *(at )?)/);Y=n&&n[1]||\"\",oe=-1<r.stack.indexOf(`\n    at`)?\" (<anonymous>)\":-1<r.stack.indexOf(\"@\")?\"@unknown:0:0\":\"\"}return`\n`+Y+e+oe}function X(e,n){if(!e||M)return\"\";var r=U.get(e);if(r!==void 0)return r;M=!0,r=Error.prepareStackTrace,Error.prepareStackTrace=void 0;var i=null;i=g.H,g.H=null,h();try{var u={DetermineComponentFrameRoot:function(){try{if(n){var b=function(){throw Error()};if(Object.defineProperty(b.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(b,[])}catch(m){var x=m}Reflect.construct(e,[],b)}else{try{b.call()}catch(m){x=m}e.call(b.prototype)}}else{try{throw Error()}catch(m){x=m}(b=e())&&typeof b.catch==\"function\"&&b.catch(function(){})}}catch(m){if(m&&x&&typeof m.stack==\"string\")return[m.stack,x.stack]}return[null,null]}};u.DetermineComponentFrameRoot.displayName=\"DetermineComponentFrameRoot\";var c=Object.getOwnPropertyDescriptor(u.DetermineComponentFrameRoot,\"name\");c&&c.configurable&&Object.defineProperty(u.DetermineComponentFrameRoot,\"name\",{value:\"DetermineComponentFrameRoot\"});var o=u.DetermineComponentFrameRoot(),f=o[0],y=o[1];if(f&&y){var s=f.split(`\n`),p=y.split(`\n`);for(o=c=0;c<s.length&&!s[c].includes(\"DetermineComponentFrameRoot\");)c++;for(;o<p.length&&!p[o].includes(\"DetermineComponentFrameRoot\");)o++;if(c===s.length||o===p.length)for(c=s.length-1,o=p.length-1;1<=c&&0<=o&&s[c]!==p[o];)o--;for(;1<=c&&0<=o;c--,o--)if(s[c]!==p[o]){if(c!==1||o!==1)do if(c--,o--,0>o||s[c]!==p[o]){var N=`\n`+s[c].replace(\" at new \",\" at \");return e.displayName&&N.includes(\"<anonymous>\")&&(N=N.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&U.set(e,N),N}while(1<=c&&0<=o);break}}}finally{M=!1,g.H=i,ge(),Error.prepareStackTrace=r}return s=(s=e?e.displayName||e.name:\"\")?w(s):\"\",typeof e==\"function\"&&U.set(e,s),s}function S(e){if(e==null)return\"\";if(typeof e==\"function\"){var n=e.prototype;return X(e,!(!n||!n.isReactComponent))}if(typeof e==\"string\")return w(e);switch(e){case W:return w(\"Suspense\");case V:return w(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case A:return e=X(e.render,!1),e;case R:return S(e.type);case P:n=e._payload,e=e._init;try{return S(e(n))}catch{}}return\"\"}function k(){var e=g.A;return e===null?null:e.getOwner()}function _e(e){if(L.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function pe(e,n){function r(){ae||(ae=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",n))}r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}function ye(){var e=a(this.type);return ie[e]||(ie[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function Ee(e,n,r,i,u,c){return r=c.ref,e={$$typeof:j,type:e,key:n,props:c,_owner:u},(r!==void 0?r:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:ye}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function ve(e,n,r,i,u,c){if(typeof e==\"string\"||typeof e==\"function\"||e===O||e===K||e===H||e===W||e===V||e===Ce||typeof e==\"object\"&&e!==null&&(e.$$typeof===P||e.$$typeof===R||e.$$typeof===F||e.$$typeof===q||e.$$typeof===A||e.$$typeof===ke||e.getModuleId!==void 0)){var o=n.children;if(o!==void 0)if(i)if(D(o)){for(i=0;i<o.length;i++)B(o[i],e);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else B(o,e)}else o=\"\",(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\"),e===null?i=\"null\":D(e)?i=\"array\":e!==void 0&&e.$$typeof===j?(i=\"<\"+(a(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):i=typeof e,console.error(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",i,o);if(L.call(n,\"key\")){o=a(e);var f=Object.keys(n).filter(function(s){return s!==\"key\"});i=0<f.length?\"{key: someKey, \"+f.join(\": ..., \")+\": ...}\":\"{key: someKey}\",ce[o+i]||(f=0<f.length?\"{\"+f.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,o,f,o),ce[o+i]=!0)}if(o=null,r!==void 0&&(d(r),o=\"\"+r),_e(n)&&(d(n.key),o=\"\"+n.key),\"key\"in n){r={};for(var y in n)y!==\"key\"&&(r[y]=n[y])}else r=n;return o&&pe(r,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),Ee(e,o,c,u,k(),r)}function B(e,n){if(typeof e==\"object\"&&e&&e.$$typeof!==Te){if(D(e))for(var r=0;r<e.length;r++){var i=e[r];T(i)&&G(i,n)}else if(T(e))e._store&&(e._store.validated=1);else if(e===null||typeof e!=\"object\"?r=null:(r=I&&e[I]||e[\"@@iterator\"],r=typeof r==\"function\"?r:null),typeof r==\"function\"&&r!==e.entries&&(r=r.call(e),r!==e))for(;!(e=r.next()).done;)T(e.value)&&G(e.value,n)}}function T(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===j}function G(e,n){if(e._store&&!e._store.validated&&e.key==null&&(e._store.validated=1,n=Ne(n),!le[n])){le[n]=!0;var r=\"\";e&&e._owner!=null&&e._owner!==k()&&(r=null,typeof e._owner.tag==\"number\"?r=a(e._owner.type):typeof e._owner.name==\"string\"&&(r=e._owner.name),r=\" It was passed a child from \"+r+\".\");var i=g.getCurrentStack;g.getCurrentStack=function(){var u=S(e.type);return i&&(u+=i()||\"\"),u},console.error('Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',n,r),g.getCurrentStack=i}}function Ne(e){var n=\"\",r=k();return r&&(r=a(r.type))&&(n=`\n\nCheck the render method of \\``+r+\"`.\"),n||(e=a(e))&&(n=`\n\nCheck the top-level render call using <`+e+\">.\"),n}var we=de(),j=Symbol.for(\"react.transitional.element\"),xe=Symbol.for(\"react.portal\"),O=Symbol.for(\"react.fragment\"),H=Symbol.for(\"react.strict_mode\"),K=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),F=Symbol.for(\"react.context\"),A=Symbol.for(\"react.forward_ref\"),W=Symbol.for(\"react.suspense\"),V=Symbol.for(\"react.suspense_list\"),R=Symbol.for(\"react.memo\"),P=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),I=Symbol.iterator,Se=Symbol.for(\"react.client.reference\"),g=we.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=Object.prototype.hasOwnProperty,_=Object.assign,ke=Symbol.for(\"react.client.reference\"),D=Array.isArray,v=0,Z,J,Q,ee,ne,re,te;E.__reactDisabledLog=!0;var Y,oe,M=!1,U=new(typeof WeakMap==\"function\"?WeakMap:Map),Te=Symbol.for(\"react.client.reference\"),ae,ie={},ce={},le={};z.Fragment=O,z.jsxDEV=function(e,n,r,i,u,c){return ve(e,n,r,i,u,c)}})()});var be=$((Ge,me)=>{\"use strict\";me.exports=fe()});var $e={};Re($e,{default:()=>Ue,frontmatter:()=>Ye});var l=Pe(be()),Ye={title:\"English Only Test Article\",slug:\"english-only-test\",description:\"This article is only available in English to test the language switching fallback functionality.\",coverImage:\"https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop\",author:\"Test Author\",authorImage:\"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\",publishedAt:\"2025-01-17\",featured:!1,tags:[\"test\",\"english-only\",\"language-switching\"]};function he(a){let t=Object.assign({h1:\"h1\",p:\"p\",h2:\"h2\",ol:\"ol\",li:\"li\",strong:\"strong\",ul:\"ul\",code:\"code\"},a.components);return(0,l.jsxDEV)(l.Fragment,{children:[(0,l.jsxDEV)(t.h1,{children:\"English Only Test Article\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.p,{children:\"This article is specifically created to test the language switching functionality when content is not available in all languages.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.h2,{children:\"Purpose\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.p,{children:\"When a user tries to switch to Chinese (\\u4E2D\\u6587) while viewing this article, they should be redirected to the Chinese blog list page since this article doesn't have a Chinese version.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.h2,{children:\"Testing Scenarios\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.ol,{children:[`\n`,(0,l.jsxDEV)(t.li,{children:[(0,l.jsxDEV)(t.strong,{children:\"Direct Language Switch\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:23,columnNumber:4},this),\": Use the header language selector\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.li,{children:[(0,l.jsxDEV)(t.strong,{children:\"Language Versions Indicator\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:24,columnNumber:4},this),\": Use the language versions component on this page\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.li,{children:[(0,l.jsxDEV)(t.strong,{children:\"Fallback Behavior\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:25,columnNumber:4},this),\": Verify that users are redirected appropriately\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:25,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.h2,{children:\"Expected Behavior\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.ul,{children:[`\n`,(0,l.jsxDEV)(t.li,{children:\"\\u2705 English version should be accessible\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.li,{children:\"\\u274C Chinese version should not exist\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.li,{children:[\"\\u{1F504} Switching to Chinese should redirect to \",(0,l.jsxDEV)(t.code,{children:\"/zh/blogs\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:31,columnNumber:46},this),\" with a notification\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:31,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,l.jsxDEV)(t.p,{children:\"This helps ensure our intelligent language switching system works correctly in all scenarios.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:33,columnNumber:1},this)]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\",lineNumber:1,columnNumber:1},this)}function Me(a={}){let{wrapper:t}=a.components||{};return t?(0,l.jsxDEV)(t,Object.assign({},a,{children:(0,l.jsxDEV)(he,a,void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\"},this)}),void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-1c33af78-1e5c-4d77-8474-33ecebc04818.mdx\"},this):he(a)}var Ue=Me;return De($e);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blogs/en/english-only-test.mdx", "_raw": {"sourceFilePath": "blogs/en/english-only-test.mdx", "sourceFileName": "english-only-test.mdx", "sourceFileDir": "blogs/en", "contentType": "mdx", "flattenedPath": "blogs/en/english-only-test"}, "type": "Blog", "lang": "en", "url": "/blogs/en/english-only-test", "createdAt": "2025-07-17T15:00:19.592Z"}