{"title": "Test Video CDN", "slug": "test-video-cdn", "description": "Testing video with CDN URL", "coverImage": "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop", "author": "Test Team", "authorImage": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": true, "tags": ["test", "video", "cdn"], "body": {"raw": "\n# Test Video CDN\n\nTesting video with your CDN URL.\n\n## CDN 视频测试\n\n<Video\n  src=\"https://cdn.windflow.dev/test/video-for-test-1.mp4?v=3\"\n  poster=\"https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop\"\n  controls\n/>\n\nThis should load your CDN video directly.\n", "code": "var Component=(()=>{var ke=Object.create;var x=Object.defineProperty;var Oe=Object.getOwnPropertyDescriptor;var Ae=Object.getOwnPropertyNames;var Re=Object.getPrototypeOf,Ve=Object.prototype.hasOwnProperty;var D=(n,a)=>()=>(a||n((a={exports:{}}).exports,a),a.exports),We=(n,a)=>{for(var i in a)x(n,i,{get:a[i],enumerable:!0})},ue=(n,a,i,_)=>{if(a&&typeof a==\"object\"||typeof a==\"function\")for(let E of Ae(a))!Ve.call(n,E)&&E!==i&&x(n,E,{get:()=>a[E],enumerable:!(_=Oe(a,E))||_.enumerable});return n};var Pe=(n,a,i)=>(i=n!=null?ke(Re(n)):{},ue(a||!n||!n.__esModule?x(i,\"default\",{value:n,enumerable:!0}):i,n)),Ye=n=>ue(x({},\"__esModule\",{value:!0}),n);var fe=D((Ge,le)=>{le.exports=React});var de=D(z=>{\"use strict\";(function(){function n(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===Te?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case O:return\"Fragment\";case Ce:return\"Portal\";case q:return\"Profiler\";case K:return\"StrictMode\";case R:return\"Suspense\";case V:return\"SuspenseList\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case I:return(e.displayName||\"Context\")+\".Provider\";case B:return(e._context.displayName||\"Context\")+\".Consumer\";case A:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case W:return r=e.displayName||null,r!==null?r:n(e.type)||\"Memo\";case P:r=e._payload,e=e._init;try{return n(e(r))}catch{}}return null}function a(e){return\"\"+e}function i(e){try{a(e);var r=!1}catch{r=!0}if(r){r=console;var o=r.error,s=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",s),a(e)}}function _(){}function E(){if(y===0){Z=console.log,J=console.info,Q=console.warn,ee=console.error,re=console.group,oe=console.groupCollapsed,te=console.groupEnd;var e={configurable:!0,enumerable:!0,value:_,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}y++}function pe(){if(y--,y===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:v({},e,{value:Z}),info:v({},e,{value:J}),warn:v({},e,{value:Q}),error:v({},e,{value:ee}),group:v({},e,{value:re}),groupCollapsed:v({},e,{value:oe}),groupEnd:v({},e,{value:te})})}0>y&&console.error(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}function N(e){if(M===void 0)try{throw Error()}catch(o){var r=o.stack.trim().match(/\\n( *(at )?)/);M=r&&r[1]||\"\",ne=-1<o.stack.indexOf(`\n    at`)?\" (<anonymous>)\":-1<o.stack.indexOf(\"@\")?\"@unknown:0:0\":\"\"}return`\n`+M+e+ne}function X(e,r){if(!e||U)return\"\";var o=$.get(e);if(o!==void 0)return o;U=!0,o=Error.prepareStackTrace,Error.prepareStackTrace=void 0;var s=null;s=p.H,p.H=null,E();try{var l={DetermineComponentFrameRoot:function(){try{if(r){var m=function(){throw Error()};if(Object.defineProperty(m.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(m,[])}catch(b){var C=b}Reflect.construct(e,[],m)}else{try{m.call()}catch(b){C=b}e.call(m.prototype)}}else{try{throw Error()}catch(b){C=b}(m=e())&&typeof m.catch==\"function\"&&m.catch(function(){})}}catch(b){if(b&&C&&typeof b.stack==\"string\")return[b.stack,C.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName=\"DetermineComponentFrameRoot\";var c=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,\"name\");c&&c.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,\"name\",{value:\"DetermineComponentFrameRoot\"});var t=l.DetermineComponentFrameRoot(),d=t[0],h=t[1];if(d&&h){var u=d.split(`\n`),g=h.split(`\n`);for(t=c=0;c<u.length&&!u[c].includes(\"DetermineComponentFrameRoot\");)c++;for(;t<g.length&&!g[t].includes(\"DetermineComponentFrameRoot\");)t++;if(c===u.length||t===g.length)for(c=u.length-1,t=g.length-1;1<=c&&0<=t&&u[c]!==g[t];)t--;for(;1<=c&&0<=t;c--,t--)if(u[c]!==g[t]){if(c!==1||t!==1)do if(c--,t--,0>t||u[c]!==g[t]){var w=`\n`+u[c].replace(\" at new \",\" at \");return e.displayName&&w.includes(\"<anonymous>\")&&(w=w.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&$.set(e,w),w}while(1<=c&&0<=t);break}}}finally{U=!1,p.H=s,pe(),Error.prepareStackTrace=o}return u=(u=e?e.displayName||e.name:\"\")?N(u):\"\",typeof e==\"function\"&&$.set(e,u),u}function T(e){if(e==null)return\"\";if(typeof e==\"function\"){var r=e.prototype;return X(e,!(!r||!r.isReactComponent))}if(typeof e==\"string\")return N(e);switch(e){case R:return N(\"Suspense\");case V:return N(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case A:return e=X(e.render,!1),e;case W:return T(e.type);case P:r=e._payload,e=e._init;try{return T(e(r))}catch{}}return\"\"}function S(){var e=p.A;return e===null?null:e.getOwner()}function ve(e){if(F.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function ge(e,r){function o(){ae||(ae=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function he(){var e=n(this.type);return se[e]||(se[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function _e(e,r,o,s,l,c){return o=c.ref,e={$$typeof:k,type:e,key:r,props:c,_owner:l},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:he}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function ye(e,r,o,s,l,c){if(typeof e==\"string\"||typeof e==\"function\"||e===O||e===q||e===K||e===R||e===V||e===xe||typeof e==\"object\"&&e!==null&&(e.$$typeof===P||e.$$typeof===W||e.$$typeof===I||e.$$typeof===B||e.$$typeof===A||e.$$typeof===Se||e.getModuleId!==void 0)){var t=r.children;if(t!==void 0)if(s)if(Y(t)){for(s=0;s<t.length;s++)G(t[s],e);Object.freeze&&Object.freeze(t)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else G(t,e)}else t=\"\",(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(t+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\"),e===null?s=\"null\":Y(e)?s=\"array\":e!==void 0&&e.$$typeof===k?(s=\"<\"+(n(e.type)||\"Unknown\")+\" />\",t=\" Did you accidentally export a JSX literal instead of a component?\"):s=typeof e,console.error(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",s,t);if(F.call(r,\"key\")){t=n(e);var d=Object.keys(r).filter(function(u){return u!==\"key\"});s=0<d.length?\"{key: someKey, \"+d.join(\": ..., \")+\": ...}\":\"{key: someKey}\",ce[t+s]||(d=0<d.length?\"{\"+d.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,s,t,d,t),ce[t+s]=!0)}if(t=null,o!==void 0&&(i(o),t=\"\"+o),ve(r)&&(i(r.key),t=\"\"+r.key),\"key\"in r){o={};for(var h in r)h!==\"key\"&&(o[h]=r[h])}else o=r;return t&&ge(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),_e(e,t,c,l,S(),o)}function G(e,r){if(typeof e==\"object\"&&e&&e.$$typeof!==je){if(Y(e))for(var o=0;o<e.length;o++){var s=e[o];j(s)&&H(s,r)}else if(j(e))e._store&&(e._store.validated=1);else if(e===null||typeof e!=\"object\"?o=null:(o=L&&e[L]||e[\"@@iterator\"],o=typeof o==\"function\"?o:null),typeof o==\"function\"&&o!==e.entries&&(o=o.call(e),o!==e))for(;!(e=o.next()).done;)j(e.value)&&H(e.value,r)}}function j(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===k}function H(e,r){if(e._store&&!e._store.validated&&e.key==null&&(e._store.validated=1,r=we(r),!ie[r])){ie[r]=!0;var o=\"\";e&&e._owner!=null&&e._owner!==S()&&(o=null,typeof e._owner.tag==\"number\"?o=n(e._owner.type):typeof e._owner.name==\"string\"&&(o=e._owner.name),o=\" It was passed a child from \"+o+\".\");var s=p.getCurrentStack;p.getCurrentStack=function(){var l=T(e.type);return s&&(l+=s()||\"\"),l},console.error('Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',r,o),p.getCurrentStack=s}}function we(e){var r=\"\",o=S();return o&&(o=n(o.type))&&(r=`\n\nCheck the render method of \\``+o+\"`.\"),r||(e=n(e))&&(r=`\n\nCheck the top-level render call using <`+e+\">.\"),r}var Ne=fe(),k=Symbol.for(\"react.transitional.element\"),Ce=Symbol.for(\"react.portal\"),O=Symbol.for(\"react.fragment\"),K=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var B=Symbol.for(\"react.consumer\"),I=Symbol.for(\"react.context\"),A=Symbol.for(\"react.forward_ref\"),R=Symbol.for(\"react.suspense\"),V=Symbol.for(\"react.suspense_list\"),W=Symbol.for(\"react.memo\"),P=Symbol.for(\"react.lazy\"),xe=Symbol.for(\"react.offscreen\"),L=Symbol.iterator,Te=Symbol.for(\"react.client.reference\"),p=Ne.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=Object.prototype.hasOwnProperty,v=Object.assign,Se=Symbol.for(\"react.client.reference\"),Y=Array.isArray,y=0,Z,J,Q,ee,re,oe,te;_.__reactDisabledLog=!0;var M,ne,U=!1,$=new(typeof WeakMap==\"function\"?WeakMap:Map),je=Symbol.for(\"react.client.reference\"),ae,se={},ce={},ie={};z.Fragment=O,z.jsxDEV=function(e,r,o,s,l,c){return ye(e,r,o,s,l,c)}})()});var me=D((Ke,be)=>{\"use strict\";be.exports=de()});var ze={};We(ze,{default:()=>$e,frontmatter:()=>Me});var f=Pe(me()),Me={title:\"Test Video CDN\",slug:\"test-video-cdn\",description:\"Testing video with CDN URL\",coverImage:\"https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop\",author:\"Test Team\",authorImage:\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face\",publishedAt:\"2025-01-17\",featured:!0,tags:[\"test\",\"video\",\"cdn\"]};function Ee(n){let a=Object.assign({h1:\"h1\",p:\"p\",h2:\"h2\"},n.components),{Video:i}=a;return i||De(\"Video\",!0,\"19:1-23:3\"),(0,f.jsxDEV)(f.Fragment,{children:[(0,f.jsxDEV)(a.h1,{children:\"Test Video CDN\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,f.jsxDEV)(a.p,{children:\"Testing video with your CDN URL.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,f.jsxDEV)(a.h2,{children:\"CDN \\u89C6\\u9891\\u6D4B\\u8BD5\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,f.jsxDEV)(i,{src:\"https://cdn.windflow.dev/test/video-for-test-1.mp4?v=3\",poster:\"https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop\",controls:!0},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,f.jsxDEV)(a.p,{children:\"This should load your CDN video directly.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\",lineNumber:25,columnNumber:1},this)]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\",lineNumber:1,columnNumber:1},this)}function Ue(n={}){let{wrapper:a}=n.components||{};return a?(0,f.jsxDEV)(a,Object.assign({},n,{children:(0,f.jsxDEV)(Ee,n,void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\"},this)}),void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx\"},this):Ee(n)}var $e=Ue;function De(n,a,i){throw new Error(\"Expected \"+(a?\"component\":\"object\")+\" `\"+n+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(i?\"\\nIt\\u2019s referenced in your code at `\"+i+\"` in `D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\products\\\\en\\\\_mdx_bundler_entry_point-3480e9fd-3b18-488c-bb05-fe9e4aa608ae.mdx`\":\"\"))}return Ye(ze);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "products/en/test-video-cdn.mdx", "_raw": {"sourceFilePath": "products/en/test-video-cdn.mdx", "sourceFileName": "test-video-cdn.mdx", "sourceFileDir": "products/en", "contentType": "mdx", "flattenedPath": "products/en/test-video-cdn"}, "type": "Product", "lang": "en", "url": "/products/en/test-video-cdn", "createdAt": "2025-07-17T17:05:38.557Z"}