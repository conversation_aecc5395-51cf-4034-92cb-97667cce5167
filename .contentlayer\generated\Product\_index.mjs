// NOTE This file is auto-generated by Contentlayer

import products__en__aiContentGeneratorMdx from './products__en__ai-content-generator.mdx.json' assert { type: 'json' }
import products__en__testVideoCdnMdx from './products__en__test-video-cdn.mdx.json' assert { type: 'json' }
import products__en__videoDemoExampleMdx from './products__en__video-demo-example.mdx.json' assert { type: 'json' }
import products__zh__aiNeiRongShengChengQiMdx from './products__zh__ai-nei-rong-sheng-cheng-qi.mdx.json' assert { type: 'json' }

export const allProducts = [products__en__aiContentGeneratorMdx, products__en__testVideoCdnMdx, products__en__videoDemoExampleMdx, products__zh__aiNeiRongShengChengQiMdx]
