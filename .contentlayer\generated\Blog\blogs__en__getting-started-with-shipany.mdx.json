{"title": "Getting Started with ShipAny: Build Your AI SaaS in Hours", "slug": "getting-started-with-shipany", "description": "Learn how to quickly build and deploy your AI SaaS application using ShipAny's powerful template and components.", "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop", "author": "ShipAny Team", "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": true, "tags": ["tutorial", "getting-started", "ai-saas"], "body": {"raw": "\n# Getting Started with <PERSON><PERSON><PERSON>\n\nWelcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.\n\n## What is ShipAny?\n\nShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.\n\n## Key Features\n\n- **🚀 Rapid Development**: Pre-built components and templates\n- **🤖 AI Integration**: Ready-to-use AI SDK integrations\n- **💳 Payment Processing**: Stripe integration out of the box\n- **🌍 Internationalization**: Multi-language support\n- **📱 Responsive Design**: Mobile-first approach\n- **🔐 Authentication**: Secure user management\n\n## Quick Start\n\n### 1. Clone the Repository\n\n```bash\ngit clone https://github.com/shipany/shipany-template\ncd shipany-template\n```\n\n### 2. Install Dependencies\n\n```bash\npnpm install\n```\n\n### 3. Set Up Environment Variables\n\nCreate a `.env.local` file with your configuration:\n\n```env\nNEXT_PUBLIC_WEB_URL=http://localhost:3000\nDATABASE_URL=your_database_url\nNEXTAUTH_SECRET=your_secret\nSTRIPE_SECRET_KEY=your_stripe_key\n```\n\n### 4. Run the Development Server\n\n```bash\npnpm dev\n```\n\n## Building Your First Feature\n\nLet's create a simple AI-powered text generator:\n\n### 1. Create the API Route\n\n```typescript\n// app/api/generate/route.ts\nimport { openai } from '@ai-sdk/openai'\nimport { generateText } from 'ai'\n\nexport async function POST(request: Request) {\n  const { prompt } = await request.json()\n  \n  const { text } = await generateText({\n    model: openai('gpt-3.5-turbo'),\n    prompt: `Generate creative content based on: ${prompt}`,\n  })\n  \n  return Response.json({ text })\n}\n```\n\n### 2. Create the Frontend Component\n\n```tsx\n'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\n\nexport function TextGenerator() {\n  const [prompt, setPrompt] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const handleGenerate = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt }),\n      })\n      const data = await response.json()\n      setResult(data.text)\n    } catch (error) {\n      console.error('Error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <Textarea\n        placeholder=\"Enter your prompt...\"\n        value={prompt}\n        onChange={(e) => setPrompt(e.target.value)}\n      />\n      <Button onClick={handleGenerate} disabled={loading}>\n        {loading ? 'Generating...' : 'Generate'}\n      </Button>\n      {result && (\n        <div className=\"p-4 bg-muted rounded-lg\">\n          {result}\n        </div>\n      )}\n    </div>\n  )\n}\n```\n\n## Next Steps\n\nNow that you have the basics set up, you can:\n\n1. **Customize the UI**: Modify components to match your brand\n2. **Add More AI Features**: Integrate additional AI models\n3. **Set Up Payments**: Configure Stripe for subscriptions\n4. **Deploy**: Deploy to Vercel or your preferred platform\n\n## Conclusion\n\nShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.\n\nReady to ship your next AI SaaS? Get started with ShipAny today!\n", "code": "var Component=(()=>{var Ve=Object.create;var v=Object.defineProperty;var Ae=Object.getOwnPropertyDescriptor;var Te=Object.getOwnPropertyNames;var De=Object.getPrototypeOf,je=Object.prototype.hasOwnProperty;var U=(i,n)=>()=>(n||i((n={exports:{}}).exports,n),n.exports),Re=(i,n)=>{for(var f in n)v(i,f,{get:n[f],enumerable:!0})},se=(i,n,f,N)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let p of Te(n))!je.call(i,p)&&p!==f&&v(i,p,{get:()=>n[p],enumerable:!(N=Ae(n,p))||N.enumerable});return i};var Oe=(i,n,f)=>(f=i!=null?Ve(De(i)):{},se(n||!i||!i.__esModule?v(f,\"default\",{value:i,enumerable:!0}):f,i)),Pe=i=>se(v({},\"__esModule\",{value:!0}),i);var fe=U(($e,ue)=>{ue.exports=React});var ce=U(G=>{\"use strict\";(function(){function i(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===Ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case A:return\"Fragment\";case Ce:return\"Portal\";case L:return\"Profiler\";case z:return\"StrictMode\";case D:return\"Suspense\";case j:return\"SuspenseList\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case K:return(e.displayName||\"Context\")+\".Provider\";case X:return(e._context.displayName||\"Context\")+\".Consumer\";case T:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case R:return r=e.displayName||null,r!==null?r:i(e.type)||\"Memo\";case O:r=e._payload,e=e._init;try{return i(e(r))}catch{}}return null}function n(e){return\"\"+e}function f(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var o=r.error,d=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",d),n(e)}}function N(){}function p(){if(x===0){J=console.log,Q=console.info,Z=console.warn,ee=console.error,ne=console.group,te=console.groupCollapsed,re=console.groupEnd;var e={configurable:!0,enumerable:!0,value:N,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}x++}function he(){if(x--,x===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:_({},e,{value:J}),info:_({},e,{value:Q}),warn:_({},e,{value:Z}),error:_({},e,{value:ee}),group:_({},e,{value:ne}),groupCollapsed:_({},e,{value:te}),groupEnd:_({},e,{value:re})})}0>x&&console.error(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}function w(e){if(I===void 0)try{throw Error()}catch(o){var r=o.stack.trim().match(/\\n( *(at )?)/);I=r&&r[1]||\"\",oe=-1<o.stack.indexOf(`\n    at`)?\" (<anonymous>)\":-1<o.stack.indexOf(\"@\")?\"@unknown:0:0\":\"\"}return`\n`+I+e+oe}function $(e,r){if(!e||Y)return\"\";var o=M.get(e);if(o!==void 0)return o;Y=!0,o=Error.prepareStackTrace,Error.prepareStackTrace=void 0;var d=null;d=h.H,h.H=null,p();try{var u={DetermineComponentFrameRoot:function(){try{if(r){var m=function(){throw Error()};if(Object.defineProperty(m.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(m,[])}catch(b){var C=b}Reflect.construct(e,[],m)}else{try{m.call()}catch(b){C=b}e.call(m.prototype)}}else{try{throw Error()}catch(b){C=b}(m=e())&&typeof m.catch==\"function\"&&m.catch(function(){})}}catch(b){if(b&&C&&typeof b.stack==\"string\")return[b.stack,C.stack]}return[null,null]}};u.DetermineComponentFrameRoot.displayName=\"DetermineComponentFrameRoot\";var l=Object.getOwnPropertyDescriptor(u.DetermineComponentFrameRoot,\"name\");l&&l.configurable&&Object.defineProperty(u.DetermineComponentFrameRoot,\"name\",{value:\"DetermineComponentFrameRoot\"});var a=u.DetermineComponentFrameRoot(),c=a[0],g=a[1];if(c&&g){var s=c.split(`\n`),y=g.split(`\n`);for(a=l=0;l<s.length&&!s[l].includes(\"DetermineComponentFrameRoot\");)l++;for(;a<y.length&&!y[a].includes(\"DetermineComponentFrameRoot\");)a++;if(l===s.length||a===y.length)for(l=s.length-1,a=y.length-1;1<=l&&0<=a&&s[l]!==y[a];)a--;for(;1<=l&&0<=a;l--,a--)if(s[l]!==y[a]){if(l!==1||a!==1)do if(l--,a--,0>a||s[l]!==y[a]){var S=`\n`+s[l].replace(\" at new \",\" at \");return e.displayName&&S.includes(\"<anonymous>\")&&(S=S.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&M.set(e,S),S}while(1<=l&&0<=a);break}}}finally{Y=!1,h.H=d,he(),Error.prepareStackTrace=o}return s=(s=e?e.displayName||e.name:\"\")?w(s):\"\",typeof e==\"function\"&&M.set(e,s),s}function E(e){if(e==null)return\"\";if(typeof e==\"function\"){var r=e.prototype;return $(e,!(!r||!r.isReactComponent))}if(typeof e==\"string\")return w(e);switch(e){case D:return w(\"Suspense\");case j:return w(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return e=$(e.render,!1),e;case R:return E(e.type);case O:r=e._payload,e=e._init;try{return E(e(r))}catch{}}return\"\"}function k(){var e=h.A;return e===null?null:e.getOwner()}function _e(e){if(H.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function ye(e,r){function o(){ae||(ae=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function ge(){var e=i(this.type);return ie[e]||(ie[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function Ne(e,r,o,d,u,l){return o=l.ref,e={$$typeof:V,type:e,key:r,props:l,_owner:u},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:ge}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function xe(e,r,o,d,u,l){if(typeof e==\"string\"||typeof e==\"function\"||e===A||e===L||e===z||e===D||e===j||e===ve||typeof e==\"object\"&&e!==null&&(e.$$typeof===O||e.$$typeof===R||e.$$typeof===K||e.$$typeof===X||e.$$typeof===T||e.$$typeof===ke||e.getModuleId!==void 0)){var a=r.children;if(a!==void 0)if(d)if(P(a)){for(d=0;d<a.length;d++)B(a[d],e);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else B(a,e)}else a=\"\",(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\"),e===null?d=\"null\":P(e)?d=\"array\":e!==void 0&&e.$$typeof===V?(d=\"<\"+(i(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):d=typeof e,console.error(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",d,a);if(H.call(r,\"key\")){a=i(e);var c=Object.keys(r).filter(function(s){return s!==\"key\"});d=0<c.length?\"{key: someKey, \"+c.join(\": ..., \")+\": ...}\":\"{key: someKey}\",de[a+d]||(c=0<c.length?\"{\"+c.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,d,a,c,a),de[a+d]=!0)}if(a=null,o!==void 0&&(f(o),a=\"\"+o),_e(r)&&(f(r.key),a=\"\"+r.key),\"key\"in r){o={};for(var g in r)g!==\"key\"&&(o[g]=r[g])}else o=r;return a&&ye(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),Ne(e,a,l,u,k(),o)}function B(e,r){if(typeof e==\"object\"&&e&&e.$$typeof!==We){if(P(e))for(var o=0;o<e.length;o++){var d=e[o];W(d)&&q(d,r)}else if(W(e))e._store&&(e._store.validated=1);else if(e===null||typeof e!=\"object\"?o=null:(o=F&&e[F]||e[\"@@iterator\"],o=typeof o==\"function\"?o:null),typeof o==\"function\"&&o!==e.entries&&(o=o.call(e),o!==e))for(;!(e=o.next()).done;)W(e.value)&&q(e.value,r)}}function W(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===V}function q(e,r){if(e._store&&!e._store.validated&&e.key==null&&(e._store.validated=1,r=Se(r),!le[r])){le[r]=!0;var o=\"\";e&&e._owner!=null&&e._owner!==k()&&(o=null,typeof e._owner.tag==\"number\"?o=i(e._owner.type):typeof e._owner.name==\"string\"&&(o=e._owner.name),o=\" It was passed a child from \"+o+\".\");var d=h.getCurrentStack;h.getCurrentStack=function(){var u=E(e.type);return d&&(u+=d()||\"\"),u},console.error('Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',r,o),h.getCurrentStack=d}}function Se(e){var r=\"\",o=k();return o&&(o=i(o.type))&&(r=`\n\nCheck the render method of \\``+o+\"`.\"),r||(e=i(e))&&(r=`\n\nCheck the top-level render call using <`+e+\">.\"),r}var we=fe(),V=Symbol.for(\"react.transitional.element\"),Ce=Symbol.for(\"react.portal\"),A=Symbol.for(\"react.fragment\"),z=Symbol.for(\"react.strict_mode\"),L=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var X=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),D=Symbol.for(\"react.suspense\"),j=Symbol.for(\"react.suspense_list\"),R=Symbol.for(\"react.memo\"),O=Symbol.for(\"react.lazy\"),ve=Symbol.for(\"react.offscreen\"),F=Symbol.iterator,Ee=Symbol.for(\"react.client.reference\"),h=we.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=Object.prototype.hasOwnProperty,_=Object.assign,ke=Symbol.for(\"react.client.reference\"),P=Array.isArray,x=0,J,Q,Z,ee,ne,te,re;N.__reactDisabledLog=!0;var I,oe,Y=!1,M=new(typeof WeakMap==\"function\"?WeakMap:Map),We=Symbol.for(\"react.client.reference\"),ae,ie={},de={},le={};G.Fragment=A,G.jsxDEV=function(e,r,o,d,u,l){return xe(e,r,o,d,u,l)}})()});var me=U((qe,be)=>{\"use strict\";be.exports=ce()});var Ue={};Re(Ue,{default:()=>Me,frontmatter:()=>Ie});var t=Oe(me()),Ie={title:\"Getting Started with ShipAny: Build Your AI SaaS in Hours\",slug:\"getting-started-with-shipany\",description:\"Learn how to quickly build and deploy your AI SaaS application using ShipAny's powerful template and components.\",coverImage:\"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop\",author:\"ShipAny Team\",authorImage:\"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\",publishedAt:\"2025-01-17\",featured:!0,tags:[\"tutorial\",\"getting-started\",\"ai-saas\"]};function pe(i){let n=Object.assign({h1:\"h1\",p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",strong:\"strong\",h3:\"h3\",pre:\"pre\",code:\"code\",ol:\"ol\"},i.components);return(0,t.jsxDEV)(t.Fragment,{children:[(0,t.jsxDEV)(n.h1,{children:\"Getting Started with ShipAny\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:\"Welcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h2,{children:\"What is ShipAny?\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:\"ShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h2,{children:\"Key Features\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.ul,{children:[`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"\\u{1F680} Rapid Development\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:23,columnNumber:3},this),\": Pre-built components and templates\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"\\u{1F916} AI Integration\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:24,columnNumber:3},this),\": Ready-to-use AI SDK integrations\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"\\u{1F4B3} Payment Processing\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:25,columnNumber:3},this),\": Stripe integration out of the box\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"\\u{1F30D} Internationalization\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:26,columnNumber:3},this),\": Multi-language support\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"\\u{1F4F1} Responsive Design\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:27,columnNumber:3},this),\": Mobile-first approach\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"\\u{1F510} Authentication\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:28,columnNumber:3},this),\": Secure user management\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:28,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h2,{children:\"Quick Start\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h3,{children:\"1. Clone the Repository\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.pre,{children:(0,t.jsxDEV)(n.code,{className:\"language-bash\",children:`git clone https://github.com/shipany/shipany-template\ncd shipany-template\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:34,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h3,{children:\"2. Install Dependencies\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.pre,{children:(0,t.jsxDEV)(n.code,{className:\"language-bash\",children:`pnpm install\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:41,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h3,{children:\"3. Set Up Environment Variables\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:[\"Create a \",(0,t.jsxDEV)(n.code,{children:\".env.local\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:47,columnNumber:10},this),\" file with your configuration:\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.pre,{children:(0,t.jsxDEV)(n.code,{className:\"language-env\",children:`NEXT_PUBLIC_WEB_URL=http://localhost:3000\nDATABASE_URL=your_database_url\nNEXTAUTH_SECRET=your_secret\nSTRIPE_SECRET_KEY=your_stripe_key\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:49,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:49,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h3,{children:\"4. Run the Development Server\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:56,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.pre,{children:(0,t.jsxDEV)(n.code,{className:\"language-bash\",children:`pnpm dev\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:58,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:58,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h2,{children:\"Building Your First Feature\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:62,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:\"Let's create a simple AI-powered text generator:\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:64,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h3,{children:\"1. Create the API Route\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:66,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.pre,{children:(0,t.jsxDEV)(n.code,{className:\"language-typescript\",children:`// app/api/generate/route.ts\nimport { openai } from '@ai-sdk/openai'\nimport { generateText } from 'ai'\n\nexport async function POST(request: Request) {\n  const { prompt } = await request.json()\n  \n  const { text } = await generateText({\n    model: openai('gpt-3.5-turbo'),\n    prompt: \\`Generate creative content based on: \\${prompt}\\`,\n  })\n  \n  return Response.json({ text })\n}\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:68,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:68,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h3,{children:\"2. Create the Frontend Component\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:85,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.pre,{children:(0,t.jsxDEV)(n.code,{className:\"language-tsx\",children:`'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\n\nexport function TextGenerator() {\n  const [prompt, setPrompt] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const handleGenerate = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt }),\n      })\n      const data = await response.json()\n      setResult(data.text)\n    } catch (error) {\n      console.error('Error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <Textarea\n        placeholder=\"Enter your prompt...\"\n        value={prompt}\n        onChange={(e) => setPrompt(e.target.value)}\n      />\n      <Button onClick={handleGenerate} disabled={loading}>\n        {loading ? 'Generating...' : 'Generate'}\n      </Button>\n      {result && (\n        <div className=\"p-4 bg-muted rounded-lg\">\n          {result}\n        </div>\n      )}\n    </div>\n  )\n}\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:87,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:87,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h2,{children:\"Next Steps\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:136,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:\"Now that you have the basics set up, you can:\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:138,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.ol,{children:[`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"Customize the UI\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:140,columnNumber:4},this),\": Modify components to match your brand\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:140,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"Add More AI Features\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:141,columnNumber:4},this),\": Integrate additional AI models\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:141,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"Set Up Payments\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:142,columnNumber:4},this),\": Configure Stripe for subscriptions\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:142,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.li,{children:[(0,t.jsxDEV)(n.strong,{children:\"Deploy\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:143,columnNumber:4},this),\": Deploy to Vercel or your preferred platform\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:143,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:140,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.h2,{children:\"Conclusion\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:145,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:\"ShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:147,columnNumber:1},this),`\n`,(0,t.jsxDEV)(n.p,{children:\"Ready to ship your next AI SaaS? Get started with ShipAny today!\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:149,columnNumber:1},this)]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\",lineNumber:1,columnNumber:1},this)}function Ye(i={}){let{wrapper:n}=i.components||{};return n?(0,t.jsxDEV)(n,Object.assign({},i,{children:(0,t.jsxDEV)(pe,i,void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\"},this)}),void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\en\\\\_mdx_bundler_entry_point-d8f8f968-a66b-47f9-b1e4-52b0e2e014d5.mdx\"},this):pe(i)}var Me=Ye;return Pe(Ue);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blogs/en/getting-started-with-shipany.mdx", "_raw": {"sourceFilePath": "blogs/en/getting-started-with-shipany.mdx", "sourceFileName": "getting-started-with-shipany.mdx", "sourceFileDir": "blogs/en", "contentType": "mdx", "flattenedPath": "blogs/en/getting-started-with-shipany"}, "type": "Blog", "lang": "en", "url": "/blogs/en/getting-started-with-shipany", "createdAt": "2025-07-17T08:59:07.197Z"}