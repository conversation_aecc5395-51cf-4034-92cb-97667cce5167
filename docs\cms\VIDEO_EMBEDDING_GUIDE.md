# MDX 视频嵌入使用指南

## 概述

MDX 内容系统现已支持多种视频嵌入方式，包括自托管视频、YouTube 和 Bilibili 平台视频。本指南将详细介绍如何在 MDX 内容中嵌入和管理视频。

## 支持的视频类型

### 1. 自托管视频 (推荐)
- **格式**: MP4, WebM, OGV
- **优势**: 完全控制、无广告、快速加载
- **存储**: 上传到云存储服务 (AWS S3, 阿里云 OSS 等)

### 2. YouTube 视频
- **优势**: 全球 CDN、自动转码、丰富功能
- **适用**: 公开教程、产品演示

### 3. Bilibili 视频
- **优势**: 中国本土化、弹幕互动
- **适用**: 中文内容、技术分享

## Frontmatter 视频字段

在 MDX 文件的 frontmatter 中可以添加视频相关字段：

```yaml
---
title: "产品演示"
slug: "product-demo"
# 视频相关字段
videoUrl: "https://your-cdn.com/videos/demo.mp4"
videoThumbnail: "https://your-cdn.com/thumbnails/demo.jpg"
videoDuration: "5:30"
---
```

### 字段说明

- **videoUrl**: 自托管视频的 URL 地址
- **videoThumbnail**: 视频缩略图/封面图片
- **videoDuration**: 视频时长 (格式: "分:秒")

## 视频组件使用方法

### 1. 自托管视频组件

```mdx
<Video 
  src="https://your-cdn.com/videos/demo.mp4"
  poster="https://your-cdn.com/thumbnails/demo.jpg"
  controls
  width="100%"
  height="auto"
/>
```

#### 支持的属性

- `src`: 视频文件 URL (必需)
- `poster`: 视频封面图片 URL
- `controls`: 显示播放控件 (默认: true)
- `autoplay`: 自动播放 (默认: false)
- `muted`: 静音播放 (默认: false)
- `loop`: 循环播放 (默认: false)
- `width`: 视频宽度 (默认: "100%")
- `height`: 视频高度 (默认: "auto")

### 2. YouTube 视频组件

```mdx
<YouTube 
  videoId="dQw4w9WgXcQ" 
  title="产品演示视频"
  autoplay={false}
  controls={true}
  start={30}
  end={120}
/>
```

#### 支持的属性

- `videoId`: YouTube 视频 ID (必需)
- `title`: 视频标题
- `autoplay`: 自动播放 (默认: false)
- `muted`: 静音播放 (默认: false)
- `controls`: 显示控件 (默认: true)
- `start`: 开始时间 (秒)
- `end`: 结束时间 (秒)

### 3. Bilibili 视频组件

```mdx
<Bilibili 
  bvid="BV1xx411c7mu"
  title="技术分享视频"
  page={1}
  autoplay={false}
/>
```

#### 支持的属性

- `bvid`: Bilibili 视频 BV 号 (推荐)
- `aid`: Bilibili 视频 AV 号 (可选)
- `cid`: 视频分 P 的 CID (可选)
- `page`: 视频分 P 页码 (默认: 1)
- `title`: 视频标题
- `autoplay`: 自动播放 (默认: false)
- `muted`: 静音播放 (默认: false)

### 4. 视频集合组件

```mdx
<VideoGallery 
  columns={2}
  videos={[
    {
      type: "video",
      src: "https://your-cdn.com/video1.mp4",
      poster: "https://your-cdn.com/thumb1.jpg",
      title: "演示视频 1"
    },
    {
      type: "youtube",
      videoId: "dQw4w9WgXcQ",
      title: "YouTube 教程"
    },
    {
      type: "bilibili",
      bvid: "BV1xx411c7mu",
      title: "Bilibili 分享"
    }
  ]}
/>
```

#### 支持的属性

- `columns`: 网格列数 (1, 2, 3)
- `videos`: 视频数组，每个视频对象包含:
  - `type`: 视频类型 ("video", "youtube", "bilibili")
  - 其他属性根据类型而定

## 视频文件管理

### 1. 视频文件准备

#### 推荐规格
- **分辨率**: 1920x1080 (1080p) 或 1280x720 (720p)
- **格式**: MP4 (H.264 编码)
- **码率**: 2-5 Mbps
- **帧率**: 30fps
- **音频**: AAC 编码

#### 文件大小优化
- 使用适当的压缩比例
- 考虑提供多种分辨率
- 为移动设备优化

### 2. 上传到存储服务

#### 推荐的存储服务
- **AWS S3**: 全球 CDN，高可用性
- **阿里云 OSS**: 中国本土化，速度快
- **腾讯云 COS**: 性价比高
- **七牛云**: 专业的多媒体处理

#### 上传步骤
1. 创建存储桶/空间
2. 配置 CDN 加速
3. 设置公开访问权限
4. 上传视频文件
5. 获取公开访问 URL

### 3. 缩略图生成

#### 自动生成
```bash
# 使用 FFmpeg 生成缩略图
ffmpeg -i input.mp4 -ss 00:00:05 -vframes 1 thumbnail.jpg
```

#### 手动制作
- 尺寸: 1280x720 或 800x450
- 格式: JPG 或 PNG
- 质量: 高质量，文件大小适中

## 性能优化

### 1. 懒加载
所有视频组件都支持懒加载，只有当视频进入视口时才开始加载。

### 2. 响应式设计
视频组件自动适配不同屏幕尺寸：
- 桌面端: 全宽显示
- 平板端: 适配屏幕宽度
- 移动端: 优化触控体验

### 3. 加载状态
- 显示加载动画
- 错误处理和重试
- 优雅降级

## 最佳实践

### 1. 内容策略
- **开场吸引**: 前 5 秒要抓住观众注意力
- **内容精炼**: 控制视频时长，避免冗长
- **清晰音质**: 确保音频质量良好
- **字幕支持**: 提供多语言字幕

### 2. SEO 优化
- 提供详细的视频描述
- 使用相关关键词
- 添加视频结构化数据
- 优化缩略图

### 3. 用户体验
- 提供清晰的缩略图
- 合理设置自动播放
- 支持键盘控制
- 移动端优化

## 故障排除

### 常见问题

#### 1. 视频无法播放
- 检查视频 URL 是否正确
- 确认视频格式兼容性
- 验证网络连接

#### 2. 加载缓慢
- 优化视频文件大小
- 使用 CDN 加速
- 检查服务器带宽

#### 3. 移动端问题
- 确保视频格式兼容
- 检查自动播放策略
- 优化触控体验

### 调试方法

#### 1. 浏览器开发者工具
- 检查网络请求
- 查看控制台错误
- 分析性能指标

#### 2. 视频测试
```mdx
<!-- 简单测试视频 -->
<Video 
  src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
  controls
/>
```

## 示例代码

### 完整的视频演示页面

```mdx
---
title: "产品功能演示"
slug: "product-demo"
description: "通过视频了解我们产品的强大功能"
videoUrl: "https://your-cdn.com/videos/product-demo.mp4"
videoDuration: "8:45"
---

# 产品功能演示

## 主要功能介绍

<Video 
  src="https://your-cdn.com/videos/product-demo.mp4"
  poster="https://your-cdn.com/thumbnails/product-demo.jpg"
  controls
/>

## 详细教程

<YouTube 
  videoId="your-youtube-id" 
  title="详细使用教程"
/>

## 中文说明

<Bilibili 
  bvid="BV1xx411c7mu"
  title="中文功能说明"
/>

## 相关视频

<VideoGallery 
  columns={3}
  videos={[
    {
      type: "video",
      src: "https://your-cdn.com/video1.mp4",
      title: "基础功能"
    },
    {
      type: "video", 
      src: "https://your-cdn.com/video2.mp4",
      title: "高级功能"
    },
    {
      type: "youtube",
      videoId: "youtube-id",
      title: "用户案例"
    }
  ]}
/>
```

通过这个指南，您可以在 MDX 内容中轻松嵌入和管理各种类型的视频，为用户提供丰富的多媒体体验。
